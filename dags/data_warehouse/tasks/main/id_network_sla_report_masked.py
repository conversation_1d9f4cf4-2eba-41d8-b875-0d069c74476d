import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked, delta_tables

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.ID_NETWORK_SLA_REPORT_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.ID_NETWORK_SLA_REPORT_MASKED,
    system_ids=(constants.SystemID.ID,),
    depends_on=(
        data_warehouse.OrderSLADAG.Task.ID_NETWORK_SLA_REPORT_BASE_MASKED,
        data_warehouse.OrderSLADAG.Task.ORDER_HUB_TIMESTAMPS_FLAT_MASKED,
        data_warehouse.OrderSLADAG.Task.TRANSIT_TIME_REPORT_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
    ),
)

MAX_TRANSIT_LOOP = 5

def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env
    
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ID_NETWORK_SLA_REPORT_BASE,
                view_name="id_network_sla_report_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).TRANSIT_TIME_REPORT,
                view_name="transit_time_report",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_HUB_TIMESTAMPS_FLAT,
                view_name="order_hub_timestamps_flat",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
        ),
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="odm_order_path",
                jinja_template="""
                with base as (
                
                    select
                        base.order_id
                        , base.min_seq
                        , hubs.name as hub_name
                    from order_hub_timestamps_flat as base
                    left join hubs_enriched as hubs
                        on base.hub_id = hubs.id
                    where 
                        base.parcel_flow = 'forward_leg'
                        and hubs.facility_type != 'AIRPORT'
                
                ) 
                
                , stitched_path as (

                    select
                        order_id
                        , min_seq
                        , hub_name
                        , collect_list(hub_name)
                            over(partition by order_id order by min_seq) as order_path
                    from base
                    
                )

                select
                    order_id
                    , max_by(order_path, min_seq) as order_path 
                from stitched_path
                group by 1
                """,
            ),
            base.TransformView(
                view_name="sla_base",
                jinja_template="""
                select
                    base.*
                {%- for seq in range(1, max_transit+1) %}
                    , transit{{ seq }}.id as transit{{ seq }}_hub_id
                {%- endfor %}
                    , ttr.tracking_id
                    , if(ttr.dp_dropoff_dp_id is not null, 1, 0) as dp_flag
                    , ttr.dp_dropoff_dp_id
                    , ttr.dp_dropoff_dp_name
                    , ttr.pickup_hub_name
                    , ttr.inbound_hub_name
                    , ttr.origin_hub_name
                    , ttr.shipper_id
                    , ttr.shipper_name
                    , ttr.parent_id_coalesce
                    , ttr.parent_name_coalesce
                    , ttr.reporting_name
                    , ttr.origin_hub_region
                    , ttr.dest_hub_region
                    , case
                        when hour(base.pickup_datetime) between 0 and 17 then 'wave_0_17'
                        when hour(base.pickup_datetime) between 18 and 23 then 'wave_18_23'
                        when hour(base.start_clock_datetime) between 0 and 17 then 'wave_0_17'
                        when hour(base.start_clock_datetime) between 18 and 23 then 'wave_18_23'
                    end as wave_group
                    , ttr.nv_pickup_datetime
                    , ttr.first_valid_delivery_attempt_datetime
                    , ttr.second_valid_delivery_attempt_datetime
                    , ttr.third_valid_delivery_attempt_datetime
                    , ttr.delivery_success_datetime
                from id_network_sla_report_base as base
                left join transit_time_report as ttr
                    on base.order_id = ttr.order_id
                {%- for seq in range(1, max_transit+1) %}
                left join hubs_enriched as transit{{ seq }}
                    on base.transit{{ seq }}_hub = transit{{ seq }}.name
                {%- endfor %}
                """,
                jinja_arguments={
                    "max_transit":MAX_TRANSIT_LOOP,
                }
            ),
            base.TransformView(
                view_name="add_timestamps",
                jinja_template="""
                select
                    base.*
                    , coalesce(
                        proper_station_inbound.iv_datetime
                        , direct_ats_station_inbound.iv_datetime
                    ) as origin_station_iv_datetime
                    , coalesce(
                        origin_msh.first_poh_datetime
                        , origin_msh.trip_arrival_datetime
                        , origin_msh.ih_datetime
                    ) as origin_msh_poh_datetime
                    , origin_msh.ib_datetime as origin_msh_ib_datetime
                    , origin_msh.cs_datetime as origin_msh_cs_datetime
                    , origin_msh.iv_datetime as origin_msh_iv_datetime
                {%- for transit in range(1, max_transit+1) %}
                    , transit{{ transit }}.trip_arrival_datetime as transit{{ transit }}_poa_datetime
                    , transit{{ transit }}.ih_datetime as transit{{ transit }}_ih_datetime
                    , transit{{ transit }}.iv_datetime as transit{{ transit }}_iv_datetime
                {%- endfor %}
                    , dest_msh.trip_arrival_datetime as dest_msh_poa_datetime
                    , coalesce(dest_msh.first_poh_datetime, dest_msh.ih_datetime) as dest_msh_ih_datetime
                    , dest_msh.ib_datetime as dest_msh_ib_datetime
                    , dest_msh.cs_datetime as dest_msh_cs_datetime
                    , dest_msh.iv_datetime as dest_msh_iv_datetime
                    , coalesce(
                        dest_station.trip_arrival_datetime
                        , dest_station.ih_datetime
                    ) as dest_hub_poa_datetime
                    , dest_station.ih_datetime as dest_hub_ih_datetime
                    , dest_station.ib_datetime as dest_hub_ib_datetime
                from sla_base as base
                left join order_hub_timestamps_flat as proper_station_inbound
                    on base.order_id = proper_station_inbound.order_id
                    and base.inbound_hub_id = proper_station_inbound.hub_id
                    and proper_station_inbound.parcel_flow = 'forward_leg'
                    and base.start_clock_granular_classification in
                        ('direct_st_ib', 'msh_pu_st_ib', 'normal_st_pu_ib')
                left join order_hub_timestamps_flat as direct_ats_station_inbound
                    on base.order_id = direct_ats_station_inbound.order_id
                    and base.pickup_hub_id = direct_ats_station_inbound.hub_id
                    and direct_ats_station_inbound.parcel_flow = 'forward_leg'
                    and base.start_clock_granular_classification in ('st_pu_direct_ats')
                left join order_hub_timestamps_flat as origin_msh
                    on base.order_id = origin_msh.order_id
                    and base.origin_msh_hub_id = origin_msh.hub_id
                    and origin_msh.parcel_flow = 'forward_leg'
                {%- for transit in range(1, max_transit+1) %}
                left join order_hub_timestamps_flat as transit{{ transit }}
                    on base.order_id = transit{{ transit }}.order_id
                    and base.transit{{ transit }}_hub_id = transit{{ transit }}.hub_id
                    and transit{{ transit }}.parcel_flow = 'forward_leg'
                {%- endfor %}
                left join order_hub_timestamps_flat as dest_msh
                    on base.order_id = dest_msh.order_id
                    and base.dest_msh_hub_id = dest_msh.hub_id
                    and dest_msh.parcel_flow = 'forward_leg'
                left join order_hub_timestamps_flat as dest_station
                    on base.order_id = dest_station.order_id
                    and base.dest_hub_id = dest_station.hub_id
                    and dest_station.parcel_flow = 'forward_leg'
                """,
                jinja_arguments={
                    "max_transit":MAX_TRANSIT_LOOP,
                }
            ),
            base.TransformView(
                view_name="sla_setup",
                jinja_template="""
                select
                    *
                    , case
                        when start_clock_classification = 'pickup_station_inbound'
                        then expected_departure_datetime_to_origin_msh - interval '1' hour
                    end as target_origin_station_ib_datetime 
                    , case
                        when start_clock_granular_classification in
                            ('direct_st_ib', 'msh_pu_st_ib', 'normal_st_pu_ib')
                        then inbound_datetime
                    end as origin_station_ib_datetime
                    , expected_departure_datetime_to_origin_msh as target_origin_station_iv_datetime
                    , case
                        when start_clock_classification in
                            ('pickup_station_inbound', 'direct_station_inbound')
                        then expected_arrival_datetime_at_origin_msh + interval '30' minute
                        when start_clock_classification = 'pickup_msh_inbound'
                        then cast(date(pickup_datetime) as timestamp)
                            + cast(expected_poh_hour_adjustment || ' hour' as interval)
                    end as target_origin_msh_poh_datetime
                    , case
                        when start_clock_classification in
                            ('pickup_station_inbound', 'direct_station_inbound')
                        then expected_arrival_datetime_at_origin_msh + interval '1' hour '30' minute
                        when start_clock_classification = 'pickup_msh_inbound'
                        then cast(date(pickup_datetime) as timestamp)
                            + cast(expected_poh_hour_adjustment || ' hour' as interval) + interval '1' hour
                    end as target_origin_msh_ib_datetime
                    , case
                        when start_clock_classification in 
                            ('pickup_station_inbound', 'direct_station_inbound')
                        then expected_arrival_datetime_at_origin_msh + interval '3' hour
                        when start_clock_classification = 'pickup_msh_inbound'
                        then cast(date(pickup_datetime) as timestamp)
                            + cast(expected_poh_hour_adjustment || ' hour' as interval)
                            + interval '2' hour '30' minute
                        when start_clock_classification = 'direct_msh_inbound'
                        then inbound_datetime + interval '1' hour '30' minute
                    end as target_origin_msh_cs_datetime
                    , case
                        when crossdock_transit_flow = 'same_msh' then expected_departure_datetime_to_dest_hub
                        when crossdock_transit_flow = 'direct' then expected_departure_datetime_to_dest_msh
                        else expected_departure_datetime_to_transit1
                    end as target_origin_msh_iv_datetime
                {%- for seq in range(1, max_transit+1) %}
                    , expected_arrival_datetime_at_transit{{ seq }} as target_transit{{ seq }}_poa_datetime
                    , expected_arrival_datetime_at_transit{{ seq }}
                        + interval '1' hour as target_transit{{ seq }}_ih_datetime
                    , case
                        when crossdock_transit_flow = '{{ seq }}_transit'
                        then expected_departure_datetime_to_dest_msh
                    {%- if not loop.last %}
                        else expected_departure_datetime_to_transit{{ seq+1 }}
                    {%- endif %}
                    end as target_transit{{ seq }}_iv_datetime 
                {%- endfor %}
                    , expected_arrival_datetime_at_dest_msh as target_dest_msh_poa_datetime
                    , case
                        when crossdock_transit_flow = 'same_msh' then 
                            case
                                when start_clock_classification in 
                                    ('pickup_station_inbound', 'direct_station_inbound')
                                then expected_arrival_datetime_at_origin_msh + interval '30' minute
                                when start_clock_classification = 'pickup_msh_inbound'
                                then cast(date(pickup_datetime) as timestamp)
                                    + cast(expected_poh_hour_adjustment || ' hour' as interval)
                            end
                        else expected_arrival_datetime_at_dest_msh + interval '30' minute
                    end as target_dest_msh_ih_datetime
                    , case
                        when crossdock_transit_flow = 'same_msh' then 
                            case
                                when start_clock_classification in
                                    ('pickup_station_inbound', 'direct_station_inbound')
                                then expected_arrival_datetime_at_origin_msh + interval '1' hour '30' minute
                                when start_clock_classification = 'pickup_msh_inbound'
                                then cast(date(pickup_datetime) as timestamp)
                                    + cast(expected_poh_hour_adjustment || ' hour' as interval)
                                    + interval '1' hour
                            end
                        else expected_arrival_datetime_at_dest_msh + interval '1' hour '30' minute
                    end as target_dest_msh_ib_datetime
                    , case
                        when crossdock_transit_flow = 'same_msh' then
                            case
                                when start_clock_classification in
                                    ('pickup_station_inbound', 'direct_station_inbound')
                                then expected_arrival_datetime_at_origin_msh + interval '3' hour
                                when start_clock_classification = 'pickup_msh_inbound'
                                then cast(date(pickup_datetime) as timestamp)
                                    + cast(expected_poh_hour_adjustment || ' hour' as interval)
                                    + interval '2' hour '30' minute
                                when start_clock_classification = 'direct_msh_inbound'
                                then inbound_datetime + interval '1' hour '30' minute
                            end
                        else expected_arrival_datetime_at_dest_msh + interval '3' hour
                    end as target_dest_msh_cs_datetime
                    , expected_departure_datetime_to_dest_hub as target_dest_msh_iv_datetime
                    , expected_arrival_datetime_at_dest_hub as target_dest_hub_poa_datetime
                    , expected_arrival_datetime_at_dest_hub + interval '30' minute as target_dest_hub_ih_datetime
                    , expected_first_delivery_attempt_datetime as target_first_attempt_datetime
                from add_timestamps
                """,
                jinja_arguments={
                    "max_transit":MAX_TRANSIT_LOOP,
                }
            ),
            base.TransformView(
                view_name="sla_measured_met_calc",
                jinja_template="""
                select
                    *
                {%- for hub_type in hub_type_target_pair %}
                {%- if hub_type == 'transit' %}
                    {%- for seq in range(1, max_transit+1) %}
                    {%- for timestamp in hub_type_target_pair[hub_type] %}
                    , case
                        when target_transit{{ seq }}_{{ timestamp }}_datetime is null then 0
                        when transit{{ seq }}_{{ timestamp }}_datetime is null then 0
                        else 1
                    end as transit{{ seq }}_{{ timestamp }}_measured
                    , case
                        when target_transit{{ seq }}_{{ timestamp }}_datetime is null then null
                        when transit{{ seq }}_{{ timestamp }}_datetime is null then null
                        when transit{{ seq }}_{{ timestamp }}_datetime <= target_transit{{ seq }}_{{ timestamp }}_datetime
                        then 1
                        else 0
                    end as transit{{ seq }}_{{ timestamp }}_met
                    {%- endfor %}
                    {%- endfor %}
                {%- else %}
                    {%- for timestamp in hub_type_target_pair[hub_type] %}
                    , case
                        when target_{{ hub_type }}_{{ timestamp }}_datetime is null then 0
                        when {{ hub_type }}_{{ timestamp }}_datetime is null then 0
                        else 1
                    end as {{ hub_type }}_{{ timestamp }}_measured
                    , case
                        when target_{{ hub_type }}_{{ timestamp }}_datetime is null then null
                        when {{ hub_type }}_{{ timestamp }}_datetime is null then null
                        when {{ hub_type }}_{{ timestamp }}_datetime <= target_{{ hub_type }}_{{ timestamp }}_datetime
                        then 1
                        else 0
                    end as {{ hub_type }}_{{ timestamp }}_met
                    {%- endfor %}
                {%- endif %}
                {%- endfor %}
                    , case
                        when target_first_attempt_datetime is null then null
                        when first_valid_delivery_attempt_datetime is null then null
                        else 1
                    end as first_attempt_measured
                    , case
                        when target_first_attempt_datetime is null then null
                        when first_valid_delivery_attempt_datetime is null then null
                        when first_valid_delivery_attempt_datetime <= target_first_attempt_datetime then 1
                        else 0
                    end as first_attempt_met
                from sla_setup
                """,
                jinja_arguments={
                    "max_transit":MAX_TRANSIT_LOOP,
                    "hub_type_target_pair":{
                        "origin_station":("ib","iv"),
                        "origin_msh":("poh","ib","cs","iv"),
                        "transit":("poa","ih","iv"),
                        "dest_msh":("poa","ih","ib","cs","iv"),
                        "dest_hub":("poa","ih"),
                    },
                }
            ),
            base.TransformView(
                view_name="add_order_path",
                jinja_template="""
                select
                    base.*
                    , concat_ws(' > ', odm_order_path.order_path) as real_order_path
                    , concat(
                        base.inbound_hub_name
                        , coalesce(
                            case when base.inbound_hub_name != base.origin_msh then concat(' > ', base.origin_msh) end
                            , ''
                        )
                    {%- for transit in range(1, max_transit+1) %}
                        , coalesce(
                            case when base.transit{{ transit }}_hub is not null
                                then concat(' > ', base.transit{{ transit }}_hub) end
                            , ''
                        )
                    {%- endfor %}
                        , coalesce(
                            case when base.origin_msh != base.dest_msh then concat(' > ', base.dest_msh) end
                            , ''
                        )
                        , concat(' > ', base.dest_hub_name)
                    ) as expected_order_path
                from sla_measured_met_calc as base
                left join odm_order_path
                    on base.order_id = odm_order_path.order_id
                """,
                jinja_arguments={
                    "max_transit":MAX_TRANSIT_LOOP,
                }
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    order_id
                    , tracking_id
                    , shipper_id
                    , shipper_name
                    , parent_id_coalesce
                    , parent_name_coalesce
                    , reporting_name
                    , dp_flag
                    , dp_dropoff_dp_id
                    , dp_dropoff_dp_name
                    , pickup_hub_id
                    , pickup_hub_name
                    , inbound_hub_id
                    , inbound_hub_name
                    , origin_hub_id
                    , origin_hub_name
                    , origin_hub_region
                    , origin_msh_hub_id
                    , origin_msh
                {%- for seq in range(1, max_transit+1) %}
                    , transit{{ seq }}_hub
                    , transit{{ seq }}_hub_id
                {%- endfor %}
                    , dest_msh_hub_id
                    , dest_msh
                    , dest_hub_id
                    , dest_hub_name
                    , dest_hub_region
                    , pickup_datetime
                    , nv_pickup_datetime
                    , wave_group
                    , expected_poh_hour_adjustment
                    , inbound_datetime
                    , start_clock_datetime
                    , start_clock_classification
                    , start_clock_granular_classification
                    , crossdock_transit_flow
                    , milkrun_transit_flow
                    , cdst_data_issue
                {%- for hub_type in hub_type_target_pair %}
                {%- if hub_type == 'transit' %}
                    {%- for seq in range(1, max_transit+1) %}
                    {%- for timestamp in hub_type_target_pair[hub_type] %}
                    , target_transit{{ seq }}_{{ timestamp }}_datetime
                    , transit{{ seq }}_{{ timestamp }}_datetime
                    , transit{{ seq }}_{{ timestamp }}_measured
                    , transit{{ seq }}_{{ timestamp }}_met
                    {%- endfor %}
                    {%- endfor %}
                {%- else %}
                    {%- for timestamp in hub_type_target_pair[hub_type] %}
                    , target_{{ hub_type }}_{{ timestamp }}_datetime
                    , {{ hub_type }}_{{ timestamp }}_datetime
                    , {{ hub_type }}_{{ timestamp }}_measured
                    , {{ hub_type }}_{{ timestamp }}_met
                    {%- endfor %}
                {%- endif %}
                {%- endfor %}
                    , target_first_attempt_datetime
                    , first_valid_delivery_attempt_datetime
                    , first_attempt_measured
                    , first_attempt_met
                    , second_valid_delivery_attempt_datetime
                    , third_valid_delivery_attempt_datetime
                    , delivery_success_datetime
                    , real_order_path
                    , expected_order_path
                    , created_month
                from add_order_path
                """,
                jinja_arguments={
                    "max_transit":MAX_TRANSIT_LOOP,
                    "hub_type_target_pair":{
                        "origin_station":("ib","iv"),
                        "origin_msh":("poh","ib","cs","iv"),
                        "transit":("poa","ih","iv"),
                        "dest_msh":("poa","ih","ib","cs","iv"),
                        "dest_hub":("poa","ih"),
                    },
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ID_NETWORK_SLA_REPORT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.config("spark.sql.analyzer.maxIterations", "200").getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
