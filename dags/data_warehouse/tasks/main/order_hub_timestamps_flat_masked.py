import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked, delta_tables

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.ORDER_HUB_TIMESTAMPS_FLAT_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.ORDER_HUB_TIMESTAMPS_FLAT_MASKED,
    system_ids=(constants.SystemID.ID,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.POH_ORDER_METRICS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.POH_METRICS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.MIDDLE_MILE_TRIP_RELATIONSHIPS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_DEPARTMENT_MOVEMENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_RTS_TRIGGERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED,
        ),
    ),
)

def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env
    
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MIDDLE_MILE_TRIP_RELATIONSHIPS,
                view_name="middle_mile_trip_relationships",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MOVEMENT_TRIPS_ENRICHED,
                view_name="movement_trips_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DEPARTMENT_MOVEMENTS,
                view_name="order_department_movements",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_RTS_TRIGGERS,
                view_name="order_rts_triggers",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_ORDER_METRICS,
                view_name="poh_order_metrics",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_METRICS,
                view_name="poh_metrics",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDERS_ENRICHED,
                view_name="orders_enriched",
                system_id=system_id,
            ),
        ),
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                select
                    base.order_id
                    , if(base.entry_datetime >= rts.event_datetime, 'rts_leg', 'forward_leg') as parcel_flow
                    , base.hub_id
                    , min(base.department_sequence) as min_seq
                    , max(base.entry_datetime) 
                        filter(where base.department in ('middle_mile_staging_origin')) as last_ats_datetime
                    , max_by(base.location_id, base.entry_datetime)
                        filter(where base.department = 'middle_mile_staging_origin') as last_ats_shipment_id
                    , min(base.entry_datetime)
                        filter(where base.department = 'middle_mile') as iv_datetime
                    , min(base.entry_datetime) 
                        filter(where base.department in ('middle_mile_staging_dest', 'middle_mile_staging_transit')
                        ) as ih_datetime
                    , min_by(base.location_id, base.entry_datetime)
                        filter(where base.department in ('middle_mile_staging_dest', 'middle_mile_staging_transit')
                        ) as ih_shipment_id
                    , min(base.entry_datetime)
                        filter(where 
                            base.department in ('last_mile', 'sort')
                            or (base.department = 'first_mile' and base.location_type = 'HUB')
                        ) as ib_datetime
                from order_department_movements as base
                left join order_rts_triggers as rts
                    on base.order_id = rts.order_id
                where
                    -- Remove pickup as it will mess up the min_seq column
                    not (base.department = 'first_mile' and base.location_type = 'ROUTE')
                group by 1,2,3
                """,
            ),
            base.TransformView(
                view_name="poh_details",
                jinja_template="""
                select
                    order.order_id
                    , poh.hub_id
                    , min(order.hub_handover_id) as first_hub_handover_id
                    , min(poh.handover_time) as first_poh_datetime
                from poh_order_metrics as order
                left join poh_metrics as poh
                    on order.hub_handover_id = poh.id
                where
                    poh.hub_id is not null
                group by 1,2
                """,
            ),
            base.TransformView(
                view_name="trip_details",
                jinja_template="""
                select
                    mm_trip_relations.shipment_id
                    , trips.dest_hub_id
                    , min(trips.actual_arrival_datetime) as actual_arrival_datetime
                from middle_mile_trip_relationships as mm_trip_relations
                left join movement_trips_enriched as trips
                    on mm_trip_relations.trip_id = trips.trip_id
                where
                    trips.actual_arrival_datetime is not null
                group by 1,2
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with combined_timestamp as (

                    select
                        base.*
                        , shipments.orig_shipment_close_datetime as cs_datetime
                        , trip_arrival.actual_arrival_datetime
                        , poh.first_poh_datetime
                    from base
                    left join shipments_enriched as shipments
                        on base.last_ats_shipment_id = shipments.shipment_id
                    left join trip_details as trip_arrival
                        on base.ih_shipment_id = trip_arrival.shipment_id
                        and base.hub_id = trip_arrival.dest_hub_id
                    left join poh_details as poh
                        on base.order_id = poh.order_id
                        and base.hub_id = poh.hub_id

                )
                
                select
                    base.order_id
                    , base.parcel_flow
                    , base.min_seq
                    , base.hub_id
                    , base.first_poh_datetime
                    , base.actual_arrival_datetime as trip_arrival_datetime
                    , base.ih_datetime
                    , base.ih_shipment_id
                    , base.ib_datetime
                    , base.last_ats_datetime
                    , base.last_ats_shipment_id
                    , base.cs_datetime
                    , base.iv_datetime
                    , orders.created_month
                from combined_timestamp as base
                join orders_enriched as orders
                    on base.order_id = orders.order_id
                where
                    base.hub_id is not null
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_HUB_TIMESTAMPS_FLAT,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.config("spark.sql.analyzer.maxIterations", "200").getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
