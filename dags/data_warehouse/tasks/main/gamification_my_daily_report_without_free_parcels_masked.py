import sys

from pyspark.sql import SparkSession

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_DAILY_REPORT_WITHOUT_FREE_PARCELS_MASKED + ".py",
    task_name=data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_DAILY_REPORT_WITHOUT_FREE_PARCELS_MASKED,
    system_ids=(
        constants.SystemID.MY,
    ),
    depends_on=(
        data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_BASE_DATA_MASKED,
        data_warehouse.GamificationDAG.Task.GAMIFICATION_MY_RESERVATIONS_ENRICHED_MASKED,
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_BASE_DATA,
                view_name="orders_base_data",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_DELIVERY_RATES,
                view_name="delivery_rates",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_RESERVATION_RATES,
                view_name="reservation_rates",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_INDP_SR,
                view_name="indp_sr",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_LOGIC,
                view_name="logic",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).MY_LM_PAYROLL_DATA_FREE_PARCELS,
                view_name="free_parcels",
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.DriverProdGL(input_env, is_masked).DRIVERS, view_name="drivers"),
            base.InputTable(path=delta_tables.SortProdGL(input_env, is_masked).HUBS, view_name="hubs"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="delivery_rates_cte",
                jinja_template=""" 
                -- Get delivery rates table with start and end date
                with base as (

                    select
                        courier_type
                        , start_date
                        ,  date(lag(start_date)
                            over(partition by courier_type order by start_date desc) - interval '1' day)
                            as end_date_lag
                    from delivery_rates
                    where 
                        courier_type like '%INDEPENDENT%'
                        or courier_type like 'RTM%'
                    group by 1,2

                ),

                final as (

                    select
                        delivery_rates.courier_type
                        , delivery_rates.weight_start
                        , coalesce(delivery_rates.weight_end, 999999) as weight_end
                        , delivery_rates.category
                        , delivery_rates.ppr1
                        , delivery_rates.ppr2
                        , delivery_rates.ppr1 as ppr
                        , delivery_rates.waypoint_ppr
                        , if(delivery_rates.weight_end is null, concat('>',cast(delivery_rates.weight_start as double),'kg')
                            , concat(cast(delivery_rates.weight_start as double),'-',cast(delivery_rates.weight_end as double),'kg')
                        ) as weight_tier
                        , delivery_rates.start_date
                        , date(coalesce(base.end_date_lag, '2099-01-01')) as end_date
                    from delivery_rates
                    left join base
                        on delivery_rates.courier_type = base.courier_type
                        and delivery_rates.start_date = base.start_date
                    where 
                        delivery_rates.courier_type like '%INDEPENDENT%'
                        or delivery_rates.courier_type like 'RTM%'

                )

                select * from final
                """,
            ),

            base.TransformView(
                view_name="reservation_rates_cte",
                jinja_template=""" 
                -- Get reservation rates table with start and end date

                with base as (

                    select 
                        *
                        , date(lag(start_date)
                            over(partition by courier_type order by start_date desc) - interval '1' day)
                        as end_date_lag
                    from reservation_rates
                    where 1=1
                        and courier_type like '%INDEPENDENT%'

                )

                select
                    courier_type
                    , rsvn_ppr
                    , picked_up_parcel_ppr
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base
                """,
            ),

            base.TransformView(
                view_name="logic_cte",
                jinja_template=""" 
                -- Get logic table for different courier types with start and end date

                with base as (

                    select 
                        *
                        ,  date(lag(start_date)
                            over(partition by courier_type order by start_date desc) - interval '1' day)
                            as end_date_lag
                    from logic
                    where 1=1
                        and courier_type like '%INDEPENDENT%'

                )

                select
                    logic
                    , courier_type
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base

                """,
            ),

            base.TransformView(
                view_name="free_parcels_cte",
                jinja_template=""" 
                with base as (

                    select 
                        *
                        ,  date(lag(start_date)
                            over(partition by courier_type, free_parcel_threshold order by start_date desc)
                                - interval '1' day)
                        as end_date_lag
                    from free_parcels
                    where courier_type like 'RTM%'

                )

                select
                    courier_type
                    , free_parcel_threshold
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base

                """,
            ),

            base.TransformView(
                view_name="indp_sr_cte",
                jinja_template=""" 
                -- Get success rate to PPR mapping table with start and end date

                with base as (

                    select 
                        *
                        ,  date(lag(start_date)
                            over(partition by sr_start, sr_end, ppr_scheme order by start_date desc) - interval '1' day)
                            as end_date_lag
                    from indp_sr

                )

                select
                    ppr_scheme
                    , sr_start
                    , sr_end
                    , start_date
                    , date(coalesce(end_date_lag, '2099-01-01')) as end_date
                from base

                """,
            ),

            base.TransformView(
                view_name="indp_success_rate",
                jinja_template=""" 
                -- Calculate independent/cold chain drivers delivery success rate and the PPR they should get

                with base as (

                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , if(day(date(delivery_attempt_datetime)) <= 15,
                            concat(date_format(delivery_attempt_datetime,'yyyy-MM'),'-1')
                            ,concat(date_format(delivery_attempt_datetime,'yyyy-MM'),'-2')
                        ) as year_month_cycle
                        , created_month
                        , min(date(delivery_attempt_datetime)) as cycle_start_date
                        , max(date(delivery_attempt_datetime)) as cycle_end_date
                        , count(distinct order_id) filter(where transaction_status = 'Success') as parcels_delivered
                        , count(distinct order_id) as parcels_on_route
                        , round(count(distinct order_id) filter(where transaction_status = 'Success') / 
                            count(distinct order_id), 3) as success_rate
                    from orders_base_data
                    where 1=1
                        and orders_base_data.transaction_type = 'DD'
                        and driver_type like '%INDEPENDENT%'
                        -- Exclude transactions without timestamp
                        and delivery_attempt_datetime is not null
                    group by {{ range(1, 6) | join(',') }}

                )

                select
                    base.*
                    , ppr_scheme
                from base
                left join indp_sr_cte
                    on base.cycle_start_date >= indp_sr_cte.start_date
                    and base.cycle_start_date <= indp_sr_cte.end_date
                    and base.success_rate >= indp_sr_cte.sr_start
                    and base.success_rate < indp_sr_cte.sr_end

                """,
            ),

            base.TransformView(
                view_name="waypoint_group_cte",
                jinja_template=""" 
                -- Order_id and waypoint_id is the composite key and rank orders in same waypoint according to weight

                with dedup_rnk as (

                    select 
                        *
                        , row_number() over (partition by order_id, waypoint_id order by delivery_attempt_datetime desc)
                        as rnk
                    from orders_base_data
                    where 1=1
                        and transaction_type = 'DD'
                        and driver_type like '%INDEPENDENT%'

                ),

                dedup as (

                    select
                        order_id
                        , waypoint_id
                        , weight
                    from dedup_rnk
                    where rnk = 1

                )

                select
                    order_id
                    , waypoint_id
                    , weight
                    , row_number() over(partition by waypoint_id order by weight desc) weight_rank
                from dedup

                """,
            ),

            base.TransformView(
                view_name="independent_delivery_base",
                jinja_template=""" 
                -- Base delivery data for independent drivers, cold chain drivers and RTM drivers (Exclude Essilor)

                select
                    orders_base_data.order_id
                    , orders_base_data.delivery_attempt_datetime
                    , date(orders_base_data.delivery_attempt_datetime) as route_date
                    , orders_base_data.driver_id
                    , orders_base_data.driver_display_name
                    , orders_base_data.driver_type
                    , orders_base_data.route_id
                    , orders_base_data.weight
                    , orders_base_data.waypoint_id
                    , orders_base_data.transaction_status
                    , coalesce(
                        independent_ppr.weight_tier
                        , cold_chain_ppr.weight_tier
                        , independent_sameday_ppr.weight_tier
                    ) as weight_tier
                    -- Add category column to loop through for the column names at the end
                    , coalesce(
                        independent_ppr.category
                        , cold_chain_ppr.category
                        , independent_sameday_ppr.category
                    ) as category
                    , indp_success_rate.ppr_scheme as cycle_end_ppr_scheme
                    -- ppr_payroll is the actual PPR used to pay out the drivers at the end of the cycle.
                    -- This is driven by success rate. 
                    -- The original ppr use the highest rates and is displayed on Driver app.
                    , case 
                        when indp_success_rate.ppr_scheme = 'ppr_1' 
                        then coalesce(independent_ppr.ppr1, cold_chain_ppr.ppr1, independent_sameday_ppr.ppr, rtm_ppr.ppr)
                        when indp_success_rate.ppr_scheme = 'ppr_2'
                        then coalesce(independent_ppr.ppr2, cold_chain_ppr.ppr2, independent_sameday_ppr.ppr, rtm_ppr.ppr) 
                        when indp_success_rate.ppr_scheme is null
                        then rtm_ppr.ppr 
                    end as ppr_payroll
                    , coalesce(independent_ppr.ppr, cold_chain_ppr.ppr, independent_sameday_ppr.ppr, rtm_ppr.ppr) as ppr
                    -- Not used for independent and cold chain. Just added to the code for future proofing.
                    , coalesce(independent_ppr.waypoint_ppr, cold_chain_ppr.waypoint_ppr
                        , independent_sameday_ppr.waypoint_ppr, rtm_ppr.waypoint_ppr)
                    as waypoint_ppr
                    , 0 as free_parcel_threshold
                    , 'delivery' as payscheme_component
                    , date_format(orders_base_data.delivery_attempt_datetime, 'yyyy-MM') created_month
                from orders_base_data
                left join delivery_rates_cte as independent_ppr
                    on orders_base_data.weight > independent_ppr.weight_start
                    and orders_base_data.weight <= independent_ppr.weight_end
                    and date(orders_base_data.delivery_attempt_datetime) >= date(independent_ppr.start_date)
                    and date(orders_base_data.delivery_attempt_datetime) <= date(independent_ppr.end_date)
                    and independent_ppr.courier_type = 'INDEPENDENT'
                    and orders_base_data.driver_type not like 'COLD%'
                    and orders_base_data.driver_type not like '%SAMEDAY%'
                    and orders_base_data.driver_type not like 'RTM%'
                left join delivery_rates_cte as cold_chain_ppr
                    on orders_base_data.weight > cold_chain_ppr.weight_start
                    and orders_base_data.weight <= cold_chain_ppr.weight_end
                    and date(orders_base_data.delivery_attempt_datetime) >= date(cold_chain_ppr.start_date)
                    and date(orders_base_data.delivery_attempt_datetime) <= date(cold_chain_ppr.end_date)
                    and cold_chain_ppr.courier_type = 'COLD CHAIN - INDEPENDENT DRIVER'
                    and orders_base_data.driver_type like 'COLD%'
                left join delivery_rates_cte as independent_sameday_ppr
                    on orders_base_data.weight > independent_sameday_ppr.weight_start
                    and orders_base_data.weight <= independent_sameday_ppr.weight_end
                    and date(orders_base_data.delivery_attempt_datetime) >= date(independent_sameday_ppr.start_date)
                    and date(orders_base_data.delivery_attempt_datetime) <= date(independent_sameday_ppr.end_date)
                    and independent_sameday_ppr.courier_type like '%SAMEDAY%'
                    and orders_base_data.driver_type = independent_sameday_ppr.courier_type
                left join delivery_rates_cte as rtm_ppr
                    on orders_base_data.driver_type = rtm_ppr.courier_type
                    and rtm_ppr.courier_type like 'RTM%'
                    and date(orders_base_data.delivery_attempt_datetime) >= date(rtm_ppr.start_date)
                    and date(orders_base_data.delivery_attempt_datetime) <= date(rtm_ppr.end_date)
                left join indp_success_rate
                    on orders_base_data.driver_id = indp_success_rate.driver_id
                    and date(orders_base_data.delivery_attempt_datetime) >= indp_success_rate.cycle_start_date
                    and date(orders_base_data.delivery_attempt_datetime) <= indp_success_rate.cycle_end_date
                where 1=1
                    and orders_base_data.transaction_type = 'DD'

                    -- Filter for Indp drivers or Cold Chain drivers or Non-Essilor RTM drivers
                    and (
                        orders_base_data.driver_type like '%INDEPENDENT%' 
                        or (
                          orders_base_data.driver_type like 'RTM%'
                          and orders_base_data.driver_type not like '%ESSILOR%'
                        )
                  )

                """,
            ),

            base.TransformView(
                view_name="independent_delivery_payscheme_logic",
                jinja_template="""
                -- Apply Merged Waypoint logic to independent drivers

                select
                    independent_delivery_base.order_id
                    , independent_delivery_base.route_date
                    , independent_delivery_base.driver_id
                    , independent_delivery_base.driver_display_name
                    , independent_delivery_base.driver_type
                    , independent_delivery_base.route_id
                    , independent_delivery_base.weight
                    , independent_delivery_base.waypoint_id
                    , independent_delivery_base.weight_tier
                    , independent_delivery_base.category
                    , independent_delivery_base.transaction_status
                    , logic_cte.logic
                    , independent_delivery_base.cycle_end_ppr_scheme
                    , case 
                        when lower(logic_cte.logic) like '%merged waypoint' and waypoint_group_cte.weight_rank != 1 
                        then 0.5 * independent_delivery_base.ppr
                        else independent_delivery_base.ppr 
                    end as ppr
                    , case 
                        when lower(logic_cte.logic) like '%merged waypoint' and waypoint_group_cte.weight_rank != 1 
                        then 0.5 * independent_delivery_base.ppr_payroll
                        else independent_delivery_base.ppr_payroll 
                    end as ppr_payroll
                    , independent_delivery_base.waypoint_ppr
                    , if(waypoint_group_cte.weight_rank > 1, 1,0) as merged_deliveries_flag
                    , independent_delivery_base.free_parcel_threshold
                    , independent_delivery_base.payscheme_component
                    , independent_delivery_base.created_month
                from independent_delivery_base
                left join waypoint_group_cte
                    on independent_delivery_base.order_id = waypoint_group_cte.order_id
                    and independent_delivery_base.waypoint_id = waypoint_group_cte.waypoint_id
                left join logic_cte
                    on independent_delivery_base.driver_type = logic_cte.courier_type
                    and date(independent_delivery_base.delivery_attempt_datetime) >= logic_cte.start_date
                    and date(independent_delivery_base.delivery_attempt_datetime) <= logic_cte.end_date                

                """,
            ),

            base.TransformView(
                view_name="independent_delivery_aggregate",
                jinja_template="""
                -- Calculate delivery bonus and incentives on aggregated independent drivers level

                with independent_delivery_aggregated as (

                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , weight_tier
                        , category
                        , route_date
                        , free_parcel_threshold
                        , cycle_end_ppr_scheme
                        , logic
                        , ppr
                        , ppr_payroll
                        , waypoint_ppr
                        , created_month
                        , count(distinct order_id) filter(where transaction_status = 'Success') parcels_delivered
                        , count(distinct waypoint_id) filter(where transaction_status = 'Success') as waypoint_count
                        -- Add parcels delivered breakdown for merged and non merged deliveries
                        , count(distinct order_id) filter(
                            where transaction_status = 'Success' and merged_deliveries_flag = 1) 
                        as merged_parcels_delivered
                        , count(distinct order_id) filter(
                            where transaction_status = 'Success' and merged_deliveries_flag = 0) 
                        as non_merged_parcels_delivered
                    from independent_delivery_payscheme_logic
                    group by {{ range(1, 14) | join(',') }}

                )

                select 
                    * 
                    -- This contains total delivery bonus (merged + non merged). 
                    -- Merged only applies to independent contractors
                    , case 
                        when lower(driver_type) not like '%rtm%' then parcels_delivered * ppr 
                        else (parcels_delivered - waypoint_count) * ppr 
                    end as delivery_bonus_per_weight_tier
                    -- Add payable amount breakdown for merged deliveries and non merged deliveries
                    , case 
                        when lower(driver_type) not like '%rtm%' then merged_parcels_delivered * ppr 
                        else null
                    end as merged_delivery_bonus_per_weight_tier
                    , case 
                        when lower(driver_type) not like '%rtm%' then non_merged_parcels_delivered * ppr 
                        else null
                    end as non_merged_delivery_bonus_per_weight_tier
                    , case 
                        when lower(driver_type) not like '%rtm%' then parcels_delivered * ppr_payroll
                        else (parcels_delivered - waypoint_count) * ppr_payroll 
                    end as delivery_bonus_per_weight_tier_payroll
                    , waypoint_count * waypoint_ppr as waypoint_incentives
                from independent_delivery_aggregated

                """,
            ),

            base.TransformView(
                view_name="independent_rpu_data",
                jinja_template=""" 
                -- Calculate RPU Incentives for independent/cold chain drivers

                with independent_rpu_base as (
                -- Standardize route date using pickup scan datetime

                    select 
                        order_id
                        , date(pickup_datetime) as route_date
                        , driver_id
                        , driver_display_name
                        , driver_type
                        , route_id
                        , weight
                        , transaction_status
                        , pickup_datetime
                        , delivery_attempt_datetime
                        , 'rpu' as payscheme_component
                        , date_format(coalesce(pickup_datetime, delivery_attempt_datetime), 'yyyy-MM') created_month
                    from orders_base_data
                    where 1=1
                        and transaction_type = 'PP'
                        and driver_type like '%INDEPENDENT%'
                        -- If there is no pickup scan then the driver will not be paid for pickup
                        and pickup_datetime is not null

                ),

                independent_rpu_ppr as (

                    select
                        independent_rpu_base.order_id
                        , independent_rpu_base.route_date
                        , independent_rpu_base.driver_id
                        , independent_rpu_base.driver_display_name
                        , independent_rpu_base.driver_type
                        , independent_rpu_base.route_id
                        , independent_rpu_base.weight
                        , independent_rpu_base.transaction_status
                        , independent_rpu_base.payscheme_component
                        , independent_rpu_base. pickup_datetime
                        , independent_rpu_base.delivery_attempt_datetime
                        , independent_rpu_base.payscheme_component
                        , independent_rpu_base.created_month
                        , coalesce(
                            independent_ppr.weight_tier
                            , cold_chain_ppr.weight_tier
                            , independent_sameday_ppr.weight_tier
                        ) as weight_tier
                        -- Add category column to loop through for the column names at the end
                        , coalesce(
                            independent_ppr.category
                            , cold_chain_ppr.category
                            , independent_sameday_ppr.category
                        ) as category
                        , indp_success_rate.ppr_scheme as cycle_end_ppr_scheme
                        -- ppr_payroll is the actual PPR used to pay out the drivers at the end of the cycle
                        -- This is influenced by success rate. 
                        -- The original ppr use the highest rates and is displayed on Driver app.
                        , case 
                            when indp_success_rate.ppr_scheme = 'ppr_1' 
                            then coalesce(independent_ppr.ppr1, cold_chain_ppr.ppr1, independent_sameday_ppr.ppr)
                            when indp_success_rate.ppr_scheme = 'ppr_2'
                            then coalesce(independent_ppr.ppr2, cold_chain_ppr.ppr2, independent_sameday_ppr.ppr) 
                        end as ppr_payroll
                        , coalesce(independent_ppr.ppr, cold_chain_ppr.ppr, independent_sameday_ppr.ppr) as ppr
                    from independent_rpu_base
                    left join delivery_rates_cte as independent_ppr
                        on independent_rpu_base.weight > independent_ppr.weight_start
                        and independent_rpu_base.weight <= independent_ppr.weight_end
                        and independent_rpu_base.route_date >= date(independent_ppr.start_date)
                        and independent_rpu_base.route_date  <= date(independent_ppr.end_date)
                        and independent_ppr.courier_type = 'INDEPENDENT'
                        and independent_rpu_base.driver_type not like '%SAMEDAY%'
                        and independent_rpu_base.driver_type not like 'COLD%'
                    left join delivery_rates_cte as cold_chain_ppr
                        on independent_rpu_base.weight > cold_chain_ppr.weight_start
                        and independent_rpu_base.weight <= cold_chain_ppr.weight_end
                        and independent_rpu_base.route_date >= date(cold_chain_ppr.start_date)
                        and independent_rpu_base.route_date  <= date(cold_chain_ppr.end_date)
                        and cold_chain_ppr.courier_type = 'COLD CHAIN - INDEPENDENT DRIVER'
                        and independent_rpu_base.driver_type like 'COLD%'
                    left join delivery_rates_cte as independent_sameday_ppr
                        on independent_rpu_base.weight > independent_sameday_ppr.weight_start
                        and independent_rpu_base.weight <= independent_sameday_ppr.weight_end
                        and independent_rpu_base.route_date >= date(independent_sameday_ppr.start_date)
                        and independent_rpu_base.route_date <= date(independent_sameday_ppr.end_date)
                        and independent_sameday_ppr.courier_type like '%SAMEDAY%'
                        and independent_rpu_base.driver_type = independent_sameday_ppr.courier_type
                    left join indp_success_rate
                        on independent_rpu_base.driver_id = indp_success_rate.driver_id
                        and independent_rpu_base.route_date >= indp_success_rate.cycle_start_date
                        and independent_rpu_base.route_date <= indp_success_rate.cycle_end_date

                    ),

                independent_rpu_aggregate as (

                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , weight_tier
                        , category
                        , cycle_end_ppr_scheme
                        , logic_cte.logic
                        , ppr
                        , ppr_payroll
                        , route_date
                        , created_month
                        , count(distinct order_id) filter(where transaction_status = 'Success') 
                        as return_parcels_picked_up
                    from independent_rpu_ppr
                    left join logic_cte
                        on independent_rpu_ppr.driver_type = logic_cte.courier_type
                        and independent_rpu_ppr.route_date >= logic_cte.start_date
                        and independent_rpu_ppr.route_date <= logic_cte.end_date    
                    group by {{ range(1, 12) | join(',') }}

                )

                select 
                    * 
                    , (ppr * return_parcels_picked_up) as customer_pickup_bonus_per_weight_tier
                    , (ppr_payroll * return_parcels_picked_up) as customer_pickup_bonus_per_weight_tier_payroll
                from independent_rpu_aggregate

            """,
            ),

            base.TransformView(
                view_name="independent_reservation_data",
                jinja_template=""" 
                -- Calculate reservation incentives for independent drivers (exclude sameday driver)

                with base as (

                    select
                        route_driver_id as driver_id
                        , route_driver_name as driver_name
                        , route_driver_type as driver_type
                        , date(attempted_datetime) as route_date
                        , created_month
                        , count(reservation_id) filter(where status IN ('Success','COMPLETED')) 
                        as successful_reservations
                        , sum(payable_picked_up_orders) filter(where status IN ('Success','COMPLETED')
                        ) as payable_picked_up_orders                 
                    from reservations_enriched
                    where 1=1
                        and attempted_datetime is not null
                        and route_id is not null
                        and route_driver_type like '%INDEPENDENT%' 
                        and route_driver_type not like '%SAMEDAY%'
                    group by {{ range(1, 6) | join(',') }}

                )

                select
                    base.driver_name
                    , base.driver_id
                    , base.driver_type
                    , base.route_date
                    , 'Reservation' as weight_tier
                    , null as category
                    , indp_success_rate.ppr_scheme as cycle_end_ppr_scheme
                    , null as parcels_on_route
                    , null as parcels_delivered
                    , null as merged_parcels_delivered
                    , null as non_merged_parcels_delivered
                    , null as failed_parcel_count
                    , 0 as free_parcel_threshold
                    , null as return_parcels_picked_up
                    , null as payable_lm_parcels
                    , base.created_month
                    , sum(base.successful_reservations) as total_reservation
                    , sum(null) as delivery_bonus_per_weight_tier
                    , sum(null) as delivery_bonus_per_weight_tier_payroll
                    , sum(null) as merged_delivery_bonus_per_weight_tier
                    , sum(null) as non_merged_delivery_bonus_per_weight_tier
                    , sum(null) as customer_pickup_bonus_per_weight_tier
                    , sum(null) as customer_pickup_bonus_per_weight_tier_payroll
                    , sum(base.successful_reservations * reservation_rates_cte.rsvn_ppr 
                        + base.payable_picked_up_orders * reservation_rates_cte.picked_up_parcel_ppr
                    ) as merchant_pickup_bonus
                    , sum(null) as waypoint_incentives
                from base
                left join logic_cte
                    on base.driver_type = logic_cte.courier_type
                    and base.route_date >= logic_cte.start_date
                    and base.route_date <= logic_cte.end_date    
                left join reservation_rates_cte
                    on base.route_date >= reservation_rates_cte.start_date
                    and base.route_date <= reservation_rates_cte.end_date
                    and reservation_rates_cte.courier_type like '%INDEPENDENT%'
                    and base.driver_type = reservation_rates_cte.courier_type
                left join indp_success_rate
                    on base.driver_id = indp_success_rate.driver_id
                    and date(base.route_date) >= indp_success_rate.cycle_start_date
                    and date(base.route_date) <= indp_success_rate.cycle_end_date
                group by {{ range(1, 17) | join(',') }}

                """,
            ),

            base.TransformView(
                view_name="independent_delivery_data_final",
                jinja_template=""" 
                -- Combine independent drivers delivery and rpu incentives

                with ppr_incentives_table as (

                   select
                       coalesce(independent_delivery_aggregate.driver_display_name,
                            independent_rpu_data.driver_display_name) as driver_display_name
                       , coalesce(independent_delivery_aggregate.driver_id,
                            independent_rpu_data.driver_id) as driver_id
                       , coalesce(independent_delivery_aggregate.driver_type,
                            independent_rpu_data.driver_type) as driver_type
                       , coalesce(independent_delivery_aggregate.cycle_end_ppr_scheme,
                            independent_rpu_data.cycle_end_ppr_scheme) as cycle_end_ppr_scheme
                       , coalesce(independent_delivery_aggregate.logic,
                            independent_rpu_data.logic) as logic
                       , coalesce(independent_delivery_aggregate.route_date,
                            independent_rpu_data.route_date) as route_date
                       , coalesce(independent_delivery_aggregate.weight_tier,
                            independent_rpu_data.weight_tier) as weight_tier
                       , coalesce(independent_delivery_aggregate.category,
                           independent_rpu_data.category) as category
                       , independent_delivery_aggregate.parcels_delivered
                       , independent_delivery_aggregate.merged_parcels_delivered
                       , independent_delivery_aggregate.non_merged_parcels_delivered
                       , independent_delivery_aggregate.free_parcel_threshold
                       , independent_rpu_data.return_parcels_picked_up
                       , independent_delivery_aggregate.parcels_delivered as payable_lm_parcels
                       , null as total_reservation
                       , independent_delivery_aggregate.delivery_bonus_per_weight_tier
                       , independent_delivery_aggregate.merged_delivery_bonus_per_weight_tier
                       , independent_delivery_aggregate.non_merged_delivery_bonus_per_weight_tier
                       , independent_delivery_aggregate.delivery_bonus_per_weight_tier_payroll
                       , independent_rpu_data.customer_pickup_bonus_per_weight_tier
                       , independent_rpu_data.customer_pickup_bonus_per_weight_tier_payroll
                       , null as merchant_pickup_bonus
                       , independent_delivery_aggregate.waypoint_incentives
                       , coalesce(independent_delivery_aggregate.created_month, independent_rpu_data.created_month)
                        as created_month
                   from independent_delivery_aggregate
                   full outer join independent_rpu_data
                       on independent_delivery_aggregate.driver_id = independent_rpu_data.driver_id
                       and independent_delivery_aggregate.route_date = independent_rpu_data.route_date
                       and independent_delivery_aggregate.weight_tier = independent_rpu_data.weight_tier
                       -- Due to merged waypoint logic, same weight tier can still have two different PPR
                       and independent_delivery_aggregate.ppr = independent_rpu_data.ppr 

                ),

                parcels_on_route_agg as (

                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , date(delivery_attempt_datetime) as route_date
                        , created_month
                        , count(distinct order_id) as parcels_on_route
                    from orders_base_data
                    where 1=1
                        and orders_base_data.transaction_type = 'DD'
                        and driver_type like '%INDEPENDENT%'
                    group by {{ range(1, 6) | join(',') }}

                ),

                failed_parcels_cte as (

                    select
                        driver_id
                        , driver_display_name
                        , driver_type
                        , date(delivery_attempt_datetime) as route_date
                        , created_month
                        , count(distinct order_id) as failed_parcels
                    from orders_base_data
                    where 1=1
                        and orders_base_data.transaction_type = 'DD'
                        and transaction_status = 'Fail'
                        and driver_type like '%INDEPENDENT%'
                    group by {{ range(1, 6) | join(',') }}

                )

                select 
                    ppr_incentives_table.driver_display_name
                    , ppr_incentives_table.driver_id
                    , ppr_incentives_table.driver_type
                    , ppr_incentives_table.route_date
                    , ppr_incentives_table.cycle_end_ppr_scheme
                    , ppr_incentives_table.logic
                    , ppr_incentives_table.weight_tier
                    , ppr_incentives_table.category
                    , parcels_on_route_agg.parcels_on_route
                    , ppr_incentives_table.parcels_delivered
                    , ppr_incentives_table.merged_parcels_delivered
                    , ppr_incentives_table.non_merged_parcels_delivered
                    , coalesce(failed_parcels_cte.failed_parcels,0) as failed_parcels
                    , ppr_incentives_table.free_parcel_threshold
                    , ppr_incentives_table.return_parcels_picked_up
                    , ppr_incentives_table.payable_lm_parcels
                    , ppr_incentives_table.created_month
                    , ppr_incentives_table.total_reservation
                    , ppr_incentives_table.delivery_bonus_per_weight_tier
                    , ppr_incentives_table.merged_delivery_bonus_per_weight_tier
                    , ppr_incentives_table.non_merged_delivery_bonus_per_weight_tier
                    , ppr_incentives_table.delivery_bonus_per_weight_tier_payroll
                    , ppr_incentives_table.customer_pickup_bonus_per_weight_tier
                    , ppr_incentives_table.customer_pickup_bonus_per_weight_tier_payroll
                    , ppr_incentives_table.merchant_pickup_bonus
                    , ppr_incentives_table.waypoint_incentives
                from ppr_incentives_table
                left join parcels_on_route_agg
                    on ppr_incentives_table.driver_id = parcels_on_route_agg.driver_id
                    and ppr_incentives_table.route_date = parcels_on_route_agg.route_date
                left join failed_parcels_cte
                    on ppr_incentives_table.driver_id = failed_parcels_cte.driver_id
                    and ppr_incentives_table.route_date = failed_parcels_cte.route_date

                """,
            ),

            base.TransformView(
                view_name="rtm_essilor",
                jinja_template=""" 
                -- Calculate waypoint incentives for RTM Essilor 

                with base as (

                    select
                        driver_id
                        , driver_display_name as driver_name
                        , driver_type
                        , date(delivery_attempt_datetime) as route_date
                        , created_month
                        , count(distinct waypoint_id) filter (where transaction_status = 'Success') as waypoint_cnt
                    from orders_base_data
                    where 1=1
                        and driver_type in ('RTM RIDER - DELIVERY ESSILOR', 'RTM RIDER - PICKUP ESSILOR')
                    group by {{ range(1, 6) | join(',') }}

                )

                -- Ensure schema match for union operation later
                -- For delivery Essilor, first x daily waypoints paid at 0.2, exceeding ones paid at 0.6
                -- For pickup Essilor, daily first x waypoints free, exceeding ones paid at 0.4
                select
                    base.driver_name
                    , base.driver_id
                    , base.driver_type
                    , base.route_date
                    , 'Not Applicable' as cycle_end_ppr_scheme
                    , 'RTM - Base' as logic
                    , rtm_ppr.weight_tier as weight_tier
                    , null as category
                    , null as parcels_on_route
                    , null as parcels_delivered
                    , null as merged_parcels_delivered
                    , null as non_merged_parcels_delivered
                    , null as failed_parcel_count
                    , 0 as free_parcel_threshold
                    , null as return_parcels_picked_up
                    , null as payable_lm_parcels
                    , base.created_month
                    , null as total_reservation
                    , null as delivery_bonus_per_weight_tier
                    , null as merged_delivery_bonus_per_weight_tier
                    , null as non_merged_delivery_bonus_per_weight_tier
                    , null as delivery_bonus_per_weight_tier_payroll
                    , null as customer_pickup_bonus_per_weight_tier
                    , null as customer_pickup_bonus_per_weight_tier_payroll
                    , null as merchant_pickup_bonus                        
                    , if(base.waypoint_cnt <= rtm_free_parcel.free_parcel_threshold
                        , base.waypoint_cnt * rtm_ppr.ppr1, 
                            rtm_free_parcel.free_parcel_threshold * rtm_ppr.ppr1 
                                + (base.waypoint_cnt-rtm_free_parcel.free_parcel_threshold) * rtm_ppr.ppr2)
                    as waypoint_incentive
                from base
                left join delivery_rates_cte as rtm_ppr
                    on base.driver_type = rtm_ppr.courier_type
                    and base.route_date >= rtm_ppr.start_date
                    and base.route_date <= rtm_ppr.end_date
                left join free_parcels_cte as rtm_free_parcel
                    on base.driver_type = rtm_free_parcel.courier_type
                    and base.route_date >= rtm_free_parcel.start_date
                    and base.route_date <= rtm_free_parcel.end_date

                """,
            ),

            base.TransformView(
                view_name="all_weight_tier_base",
                jinja_template=""" 
                -- This CTE is to get all weight tiers a driver should theoratically have for each route date

                    select
                        distinct orders_base_data.driver_id
                        , orders_base_data.driver_display_name
                        , orders_base_data.driver_type
                        , date(orders_base_data.delivery_attempt_datetime) as route_date
                        , coalesce(independent_ppr.weight_tier, cold_chain_ppr.weight_tier
                            , independent_sameday_ppr.weight_tier, rtm_ppr.weight_tier) 
                        as weight_tier
                        , coalesce(independent_ppr.category, cold_chain_ppr.category
                            , independent_sameday_ppr.category, rtm_ppr.category)
                        as category
                        , indp_success_rate.ppr_scheme as cycle_end_ppr_scheme
                        , orders_base_data.created_month
                    from orders_base_data
                    left join delivery_rates_cte as independent_ppr
                        on date(orders_base_data.delivery_attempt_datetime) >= date(independent_ppr.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(independent_ppr.end_date)
                        and independent_ppr.courier_type = 'INDEPENDENT'
                        and orders_base_data.driver_type not like 'COLD%'
                        and orders_base_data.driver_type not like '%SAMEDAY%'
                        and orders_base_data.driver_type not like 'RTM%'

                    left join delivery_rates_cte as cold_chain_ppr
                        on date(orders_base_data.delivery_attempt_datetime) >= date(cold_chain_ppr.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(cold_chain_ppr.end_date)
                        and cold_chain_ppr.courier_type = 'COLD CHAIN - INDEPENDENT DRIVER'
                        and orders_base_data.driver_type like 'COLD%'

                    left join delivery_rates_cte as independent_sameday_ppr
                        on date(orders_base_data.delivery_attempt_datetime) >= date(independent_sameday_ppr.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(independent_sameday_ppr.end_date)
                        and independent_sameday_ppr.courier_type like '%SAMEDAY%'
                        and orders_base_data.driver_type = independent_sameday_ppr.courier_type

                    left join delivery_rates_cte as rtm_ppr
                        on orders_base_data.driver_type = rtm_ppr.courier_type
                        and rtm_ppr.courier_type like 'RTM%'
                        and date(orders_base_data.delivery_attempt_datetime) >= date(rtm_ppr.start_date)
                        and date(orders_base_data.delivery_attempt_datetime) <= date(rtm_ppr.end_date)

                    left join indp_success_rate
                        on orders_base_data.driver_id = indp_success_rate.driver_id
                        and date(orders_base_data.delivery_attempt_datetime) >= indp_success_rate.cycle_start_date
                        and date(orders_base_data.delivery_attempt_datetime) <= indp_success_rate.cycle_end_date

                    where (orders_base_data.driver_type like '%INDEPENDENT%' 
                        or orders_base_data.driver_type like 'RTM%')        

                """,
            ),

            base.TransformView(
                view_name="combined_data",
                jinja_template=""" 
                with delivery_combined_base as (

                    select * from independent_delivery_data_final
                    union all
                    select * from rtm_essilor

                ),

                all_weight_tier_combined_cte as (

                     select 
                        all_weight_tier_base.driver_display_name
                        , all_weight_tier_base.driver_id
                        , all_weight_tier_base.driver_type
                        , all_weight_tier_base.route_date
                        , all_weight_tier_base.weight_tier
                        , all_weight_tier_base.category
                        , all_weight_tier_base.cycle_end_ppr_scheme
                        , delivery_combined_base.parcels_on_route
                        , delivery_combined_base.parcels_delivered
                        , delivery_combined_base.merged_parcels_delivered
                        , delivery_combined_base.non_merged_parcels_delivered
                        , delivery_combined_base.failed_parcels
                        , delivery_combined_base.free_parcel_threshold
                        , delivery_combined_base.return_parcels_picked_up
                        , delivery_combined_base.payable_lm_parcels
                        , all_weight_tier_base.created_month
                        , delivery_combined_base.total_reservation
                        , delivery_combined_base.delivery_bonus_per_weight_tier
                        , delivery_combined_base.delivery_bonus_per_weight_tier_payroll
                        , delivery_combined_base.merged_delivery_bonus_per_weight_tier
                        , delivery_combined_base.non_merged_delivery_bonus_per_weight_tier
                        , delivery_combined_base.customer_pickup_bonus_per_weight_tier
                        , delivery_combined_base.customer_pickup_bonus_per_weight_tier_payroll
                        , delivery_combined_base.merchant_pickup_bonus
                        , delivery_combined_base.waypoint_incentives
                    from all_weight_tier_base
                    left join delivery_combined_base
                        on all_weight_tier_base.driver_id = delivery_combined_base.driver_id
                        and all_weight_tier_base.route_date = delivery_combined_base.route_date
                        and all_weight_tier_base.weight_tier = delivery_combined_base.weight_tier

                ),

                combined_base as (

                    select *
                    from all_weight_tier_combined_cte
                    union all
                    select * from independent_reservation_data

                ),

                merge_all as (

                    select 
                        'my' as system_id
                        , combined_base.route_date
                        , date_format(combined_base.route_date, 'yyyy-MM') as route_month
                        , combined_base.driver_id
                        , combined_base.driver_type
                        , combined_base.driver_display_name
                        , if(datediff(combined_base.route_date, 
                               from_utc_timestamp(date(drivers.employment_start_date), '{{ local_timezone }}')) > 14
                               , hubs.name ,null) as hub_name
                        , if(datediff(route_date, 
                               from_utc_timestamp(date(drivers.employment_start_date), '{{ local_timezone }}')) > 14
                               , drivers.hub_id ,null) as hub_id
                        , 'MYR' as currency
                        , combined_base.cycle_end_ppr_scheme
                        , combined_base.created_month
                        , cast(max(combined_base.free_parcel_threshold) as int) as target_parcel_count
                        , cast(max(combined_base.parcels_on_route) as int) as planned_parcel_count
                        , cast(coalesce(max(combined_base.parcels_on_route),0)
                            - coalesce(sum(combined_base.parcels_delivered),0) 
                                - coalesce(max(combined_base.failed_parcels),0) as int) 
                        as pending_parcel_count
                       , cast(sum(combined_base.parcels_delivered) as int) as delivered_parcel_count
                        , cast(max(combined_base.failed_parcels) as int) as failed_parcel_count
                        , cast(sum(combined_base.payable_lm_parcels) as int) as daily_payable_lm_parcels
                        , cast(sum(combined_base.delivery_bonus_per_weight_tier) as double) as delivery_daily_bonus
                        , cast(sum(combined_base.delivery_bonus_per_weight_tier_payroll) as double)
                        as delivery_daily_bonus_payroll
                        , cast(sum(combined_base.waypoint_incentives) as double) as waypoint_incentives
                        , cast(coalesce(sum(combined_base.delivery_bonus_per_weight_tier),0) 
                            + coalesce(sum(combined_base.merchant_pickup_bonus),0) 
                            + coalesce(sum(combined_base.customer_pickup_bonus_per_weight_tier),0) as double) 
                        as daily_bonus
                        , cast(coalesce(sum(combined_base.delivery_bonus_per_weight_tier_payroll),0) 
                            + coalesce(sum(combined_base.merchant_pickup_bonus),0) 
                            + coalesce(sum(combined_base.customer_pickup_bonus_per_weight_tier_payroll),0)
                            + coalesce(sum(combined_base.waypoint_incentives),0) as double) as daily_bonus_payroll

                        -- Update the 2 loops to use the new category column instead of hardcoding via jinja args
                        {%- for category_number in range(1, 14) %}
                         , max(combined_base.weight_tier) 
                             filter(where combined_base.category = '{{ category_number }}')
                         as category_{{ category_number }}_name
                         , cast(sum(combined_base.payable_lm_parcels) 
                             filter(where combined_base.category = '{{ category_number }}') as int) 
                         as category_{{ category_number }}_parcels_delivered
                         , cast(sum(combined_base.delivery_bonus_per_weight_tier) 
                             filter(where combined_base.category = '{{ category_number }}') as double)
                        as category_{{ category_number }}_daily_bonus

                            -- Add breakdown for Merged waypoint deliveries and normal deliveries
                        , cast(sum(combined_base.merged_parcels_delivered) 
                            filter(where combined_base.category = '{{ category_number }}') as int) 
                        as category_{{ category_number }}_merged_parcels_delivered
                        , cast(sum(combined_base.merged_delivery_bonus_per_weight_tier) 
                            filter(where combined_base.category = '{{ category_number }}') as double) 
                        as category_{{ category_number }}_merged_deliveries_daily_bonus
                        , cast(sum(combined_base.non_merged_parcels_delivered) 
                            filter(where combined_base.category = '{{ category_number }}') as int) 
                        as category_{{ category_number }}_non_merged_parcels_delivered
                        , cast(sum(combined_base.non_merged_delivery_bonus_per_weight_tier) 
                            filter(where combined_base.category = '{{ category_number }}') as double) 
                        as category_{{ category_number }}_non_merged_deliveries_daily_bonus
                       {%- endfor %}


                       , cast(sum(combined_base.return_parcels_picked_up) as int)
                       as pickedup_customer_count
                       , cast(sum(combined_base.customer_pickup_bonus_per_weight_tier) as double)
                       as pickedup_customer_amount
                       , cast(sum(combined_base.customer_pickup_bonus_per_weight_tier_payroll) as double)
                        as pickedup_customer_amount_payroll
                       , cast(sum(combined_base.total_reservation) as int) as pickedup_merchant_count
                       , cast(sum(combined_base.merchant_pickup_bonus) as double) as pickedup_merchant_amount
                       , cast(coalesce(sum(combined_base.merchant_pickup_bonus),0)
                           + coalesce(sum(combined_base.customer_pickup_bonus_per_weight_tier),0) as double)
                        as daily_pickup_bonus
                       , round(cast(sum(combined_base.parcels_delivered) 
                           / max(combined_base.parcels_on_route) as double),4)
                        as success_rate
                       , cast(sum(0) as int) as daily_parcels_delivered_for_balloon_bonus
                       , cast(sum(0) as int) as cumulative_parcels_delivered_for_balloon_bonus
                       , cast(sum(0) as double) as balloon_bonus_amount

                       {%- for i in range(5) if i > 0 %}
                       , cast(sum(0) as int) as tier_{{ i }}_balloon_targets_start
                       , cast(sum(0) as int) as tier_{{ i }}_balloon_targets_end
                       , cast(sum(0) as double) as tier_{{ i }}_balloon_bonus_amount

                       {%- endfor %}
                       , cast(sum(0) as double) as daily_balloon_bonus_amount
                    from combined_base
                    left join drivers
                        on combined_base.driver_id = drivers.id
                        and drivers.system_id = '{{ system_id }}'
                    left join hubs
                        on hubs.hub_id = drivers.hub_id
                        and hubs.system_id = '{{ system_id }}'
                    group by {{ range(1, 12) | join(',') }}

                )

                select 
                    *
                from merge_all
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper())
                    , "system_id": system_id,
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).GAMIFICATION_MY_DAILY_REPORT_WITHOUT_FREE_PARCELS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()