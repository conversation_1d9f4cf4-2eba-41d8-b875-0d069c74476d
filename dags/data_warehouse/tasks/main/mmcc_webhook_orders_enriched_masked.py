import sys

from pyspark.sql import SparkSession

from pyspark.sql import functions as F
from common.spark import util
from common import date
from data_warehouse.tasks.main import base
from dateutil.relativedelta import relativedelta
from datetime import datetime as dt
from datetime import timedelta
from metadata import constants, data_warehouse, versioned_parquet_tables_masked, delta_tables, parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_ORDERS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_ORDERS_ENRICHED_MASKED,
    depends_on=(
        data_warehouse.WebhookSnapshotDAG.Task.MMCC_WEBHOOK_ORDERS_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.CrossBorderDAG.DAG_ID,
            task_id=data_warehouse.CrossBorderDAG.Task.XB_EVENTS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CrossBorderDAG.DAG_ID,
            task_id=data_warehouse.CrossBorderDAG.Task.XB_PARCELS_ENRICHED_MASKED
        ),
    ),
    system_ids=(SystemID.GL,),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    latest_partition = "/measurement_datetime=latest/"

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).XB_PARCELS_ENRICHED + latest_partition,
                            view_name="xb_parcels_enriched",
                            input_range=lookback_ranges.input,
                            ),
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).XB_EVENTS_ENRICHED + latest_partition,
                            view_name="xb_events_enriched",
                            input_range=lookback_ranges.input,
                            ),
        ),
        version_datetime=measurement_datetime,
    )

    # Read mmcc webhook order table for orders holm-ed/lha-ed from the previous month
    holm_month = (measurement_datetime - timedelta(days=1)).strftime('%Y-%m')
    holm_month_dt = dt.strptime(holm_month, '%Y-%m').date()
    start_month_dt = holm_month_dt + relativedelta(months=-1)
    start_month = start_month_dt.strftime('%Y-%m')

    spark.read.format("parquet").load(parquet_tables_masked.DataWarehouse(input_env).MMCC_WEBHOOK_ORDERS + latest_partition) \
        .filter(F.col("holm_month") >= start_month).createOrReplaceTempView("mmcc_webhook_orders")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="holm_orders",
                jinja_template="""

                with base as (
                    -- holm = Handed Over To Last Mile , lha = Linehaul Arrived
                    select
                        IF(xb_parcels_enriched.shipper_id in (9438676, 9438639), 'Lazada XB MM', 'Tiktok XB MMCC') as shipper
                        , xb_parcels_enriched.shipper_id
                        , xb_parcels_enriched.ref_tracking_id as big_bag_no
                        , xb_parcels_enriched.total_parcel_items
                        , xb_parcels_enriched.parcel_type
                        , CASE 
                            WHEN xb_parcels_enriched.shipper_id IN (9438676, 9438639) 
                                THEN date(xb_events_enriched.linehaul_arrived_datetime)
                            ELSE date(xb_events_enriched.handed_over_to_last_mile_datetime) 
                        END as handed_over_to_last_mile_date
                        , CASE 
                            WHEN xb_parcels_enriched.shipper_id IN (9438676, 9438639) 
                                THEN date_format(xb_events_enriched.linehaul_arrived_datetime, 'yyyy-MM')
                            ELSE date_format(xb_events_enriched.handed_over_to_last_mile_datetime, 'yyyy-MM')
                        END as holm_month
                        , lower(xb_events_enriched.parcel_destination_country) as country
                        , concat(xb_parcels_enriched.from_country, ' - ' , xb_parcels_enriched.to_country) as od_pair
                    from xb_parcels_enriched
                    join xb_events_enriched on
                        xb_parcels_enriched.parcel_id = xb_events_enriched.parcel_id
                )

                select * from base
                where
                    -- to identify MMCC bags in parcels table
                    parcel_type in ('B2C Bag', 'B2B Bag') 
                    and shipper_id in (6257769, 6257706, 6257783, 6257752, 6257669, 9438676, 9438639)
                    -- to get parcels that holm-ed/lha-ed from previous month
                    and handed_over_to_last_mile_date 
                        >= date_trunc('month', date_sub(date_trunc('month', date_sub('{{measurement_datetime}}',1)), 1))
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                },
            ),

            base.TransformView(
                view_name="source_of_truth_tt",
                jinja_template="""

                {%- for status_code, status in status_map.items() %}
                (
                    select
                        big_bag_no
                        , total_parcel_items
                        , handed_over_to_last_mile_date
                        , holm_month
                        , country as system_id
                        , od_pair
                        , '{{ status }}' as status
                        , {{ status_code }} as status_code
                        , '{{ tt_action_code_map.get(status_code, "") }}' as tt_action_code_mapping
                        , shipper
                    from holm_orders
                    where shipper = 'Tiktok XB MMCC'
                )
                {% if not loop.last %} union all {% endif %}
                {%- endfor %}

                """,
                jinja_arguments={
                    "status_map": {
                        1: "Handed Over to Origin Facility",
                        6: "Export Cleared",
                        8: "Linehaul Departed",
                        9: "Linehaul Arrived",
                        7: "Customs Cleared",
                        10: "Handed Over to Last Mile",
                        44: "Linehaul Scheduled",
                        48: "Export Started",
                        49: "Import Started",
                    },
                    "tt_action_code_map": {
                        1: "cb_transport_assigned",
                        6: "cb_transport_originalport",
                        8: "cb_transport_departed",
                        9: "cb_transport_arrived",
                        7: "cb_imcustoms_finished",
                        10: "cb_transport_handover",
                        44: "cb_excustoms_start",
                        48: "cb_excustoms_finished",
                        49: "cb_imcustoms_start",
                    }
                },
            ),

             base.TransformView(
                view_name="source_of_truth_lazada",
                jinja_template="""

                {%- for status_code, status in status_map.items() %}
                (
                    select
                        big_bag_no
                        , total_parcel_items
                        , handed_over_to_last_mile_date
                        , holm_month
                        , country as system_id
                        , od_pair
                        , '{{ status }}' as status
                        , {{ status_code }} as status_code
                        , null as tt_action_code_mapping
                        , shipper
                    from holm_orders
                    where shipper = 'Lazada XB MM'
                )
                {% if not loop.last %} union all {% endif %}
                {%- endfor %}

                """,
                jinja_arguments={
                    "status_map": {
                        2: "Handed Over to Linehaul",
                        8: "Linehaul Departed",
                        9: "Linehaul Arrived",
                        44: "Linehaul Scheduled",
                    }
                },
            ),
            
            base.TransformView(
                view_name="source_of_truth",
                jinja_template="""

                select * from source_of_truth_tt

                union all

                select * from source_of_truth_lazada

                """,
            ),

            base.TransformView(
                view_name="successful_webhook",
                jinja_template="""

                select
                    big_bag_no
                    , status_code
                    -- to extract tiktok's action code from url-encoded format 
                    -- (eg. to extract delivery_start from %22action_code%22%3A%22delivery_start%)
                    , coalesce(
                        nullif(
                            regexp_extract(
                                min_by(mmcc_webhook_orders.webhook_generated_detail, mmcc_webhook_orders.event_time), 
                                '%22action_code%22%3A%22([^%"]+)', 
                                1
                            ), 
                            ''
                        ), 
                        null
                    ) as tt_action_code
                    , min_by(event_time, webhook_sent_time) as first_event_time
                    , min_by(webhook_sent_time, webhook_sent_time) as first_webhook_sent_time
                from mmcc_webhook_orders
                where response_code = 200
                group by 1,2

                """,
            ),

            base.TransformView(
                view_name="first_webhook_attempt",
                jinja_template="""

                select
                    big_bag_no
                    , status_code
                    , min_by(response_code, webhook_sent_time) as response_code
                from mmcc_webhook_orders
                group by 1,2

                """,
            ),

            base.TransformView(
                view_name="base",
                jinja_template="""

                select 
                    source_of_truth.big_bag_no
                    , source_of_truth.shipper
                    , source_of_truth.total_parcel_items
                    , source_of_truth.holm_month
                    , source_of_truth.handed_over_to_last_mile_date
                    , source_of_truth.status
                    , source_of_truth.status_code
                    , source_of_truth.tt_action_code_mapping
                    , successful_webhook.tt_action_code
                    , source_of_truth.system_id
                    , source_of_truth.od_pair
                    , if(first_webhook_attempt.big_bag_no is not null,1,0) as webhook_attempted_flag
                    , first_webhook_attempt.response_code as first_response_code
                    , successful_webhook.first_event_time
                    , successful_webhook.first_webhook_sent_time
                    , if(
                        successful_webhook.first_event_time is not null
                        and successful_webhook.first_webhook_sent_time is not null
                        , (
                            to_unix_timestamp(successful_webhook.first_webhook_sent_time)
                            - to_unix_timestamp(successful_webhook.first_event_time)
                        )/60
                        , null
                    ) as event_to_webhook_minutes_diff
                from source_of_truth  
                left join successful_webhook on
                    source_of_truth.big_bag_no = successful_webhook.big_bag_no
                    and source_of_truth.status_code = successful_webhook.status_code
                left join first_webhook_attempt on
                    source_of_truth.big_bag_no = first_webhook_attempt.big_bag_no
                    and source_of_truth.status_code = first_webhook_attempt.status_code

                """,
            ),

            base.TransformView(
                view_name="final",
                jinja_template="""

                select
                    big_bag_no
                    , shipper
                    , total_parcel_items
                    , handed_over_to_last_mile_date
                    , holm_month
                    , status
                    , status_code
                    , tt_action_code_mapping
                    , tt_action_code
                    , system_id
                    , od_pair
                    , webhook_attempted_flag
                    , first_response_code
                    , first_event_time
                    , first_webhook_sent_time
                    , event_to_webhook_minutes_diff
                    , case
                        when shipper = 'Tiktok XB MMCC' THEN '24 hours'
                        when shipper = 'Lazada XB MM' THEN '10 hours'
                    end as latency_sla
                    , case
                        when event_to_webhook_minutes_diff is null then null
                        when shipper = 'Tiktok XB MMCC' AND event_to_webhook_minutes_diff <= 24*60 THEN 1
                        when shipper = 'Lazada XB MM' AND event_to_webhook_minutes_diff <= 10*60 THEN 1
                        else 0
                    end as latency_sla_met
                from base

                """,
            ),
        ),
    )

    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MMCC_WEBHOOK_ORDERS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "shipper", "holm_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    task_config = get_task_config(input_args.env,
                                  input_args.last_measurement_datetime,
                                  input_args.measurement_datetime,
                                  input_args.enable_full_run,
                                  )
    run(spark, task_config)
    spark.stop()