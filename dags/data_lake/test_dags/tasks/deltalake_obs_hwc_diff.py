import logging
import sys

from pyspark.sql import SparkSession
from pyspark.sql import functions as F

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def compute_diff(spark, schema, table, created_month):
    result_bucket = "nv-data-prod-data-lake-inventory"

    obs_ids_path = f"gs://{result_bucket}/hwc/ids/{schema}/{table}/{created_month}"
    obs_df = spark.read.format("parquet").load(obs_ids_path)

    gcs_deltalake_bucket = "nv-data-prod-data-lake"
    delta_table_path = f"gs://{gcs_deltalake_bucket}/db/{schema}/{table}"
    df = spark.read.format("delta").load(delta_table_path)
    gcs_df = df.filter(F.col("created_month") == created_month)

    # 1. Stale records
    stale_rows_df = (
        gcs_df.alias("g")
        .join(obs_df.alias("h"), on="id", how="inner")
        .filter(F.col("g.updated_at") > F.col("h.updated_at"))
        .select("g.*")  # <- ensure only GCS columns are retained
    )

    # 2. Missing records
    missing_rows_df = gcs_df.join(obs_df, on="id", how="left_anti")

    migration_bucket = "nv-data-prod-data-lake-mig"

    missing_rows_path = f"gs://{migration_bucket}/hwc_data_patch/missing_rows/{schema}/{table}/{created_month}"
    stale_rows_path = f"gs://{migration_bucket}/hwc_data_patch/stale_rows/{schema}/{table}/{created_month}"

    (missing_rows_df.write.mode("overwrite").format("parquet").option("compression", "snappy").save(missing_rows_path))
    (missing_rows_df.write.mode("overwrite").format("parquet").option("compression", "snappy").save(stale_rows_path))


if __name__ == "__main__":
    if len(sys.argv) < 3:
        logger.error("Not enough args")
        sys.exit(1)

    spark = SparkSession.builder.getOrCreate()
    schema = sys.argv[1]
    table = sys.argv[2]
    month = sys.argv[3]

    compute_diff(spark, schema, table, month)
    spark.stop()
