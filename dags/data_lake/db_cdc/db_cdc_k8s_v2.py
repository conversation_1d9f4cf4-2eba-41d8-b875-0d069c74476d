import os
from functools import partial
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON><PERSON>perator, PythonOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.operators.bash import BashOperator
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from config.config import Config, JobType
from config.database import DatabaseConfig
from config.table_config import TableConfig
from config.schema_config import SchemaConfig
from config.spark_config import SparkConfig
from custom_operators.spark_k8s import SparkK8s
from common.airflow import db
from metadata.constants import Timeout
from metadata.schedule_groups import SCHEDULE_GROUPS
from common.stringcase import kebab_case
from common.spark import spark_app_utils
from common.utils import helper
from common.utils.gcs import get_nv_data_bucket_uri
from utils.delta_utils import check_and_extract_delta_metrics, clear_delta_callback
from utils.dag_utils import (
    create_dag, get_last_compaction_date, get_last_scheduled_execution_date,
    list_table_tasks, _get_path, list_changes, choose_spark_operator, get_schema_schedule_category
)

table_sizes_airflow = TableConfig.TABLE_SIZES


def create_cdc_dag(schema, tables):
    dag_id = f"datalake_cdc_{schema}"
    db_dag = create_dag(dag_id=dag_id)
    is_pii = schema in SchemaConfig.get_pii_schemas(Config.ENV)

    with db_dag as dag:
        connection = next((conn for conn, schemas in DatabaseConfig.MYSQL_CONNECTIONS.items() if schema in schemas),
                          None)
        gs_bucket_raw = get_nv_data_bucket_uri(env=Config.ENV, bucket_type="raw", schema=connection, strip=True)
        database = schema

        if connection == "ninjamart":
            _database = database.replace("ninjamart_", "")
        else:
            _database = database

        wait_previous_end = ExternalTaskSensor(
            task_id=f"wait_previous_end_{schema}",
            external_dag_id=f"datalake_cdc_{schema}",
            external_task_id=None,
            execution_date_fn=get_last_scheduled_execution_date,
            allowed_states=["success", "failed"],
            pool="sensor_pool",
            soft_fail=True,
            timeout=900,
            trigger_rule="all_done"
        )

        wait_task = BashOperator(
            task_id=f"cdc_wait",
            bash_command=f"sleep {get_schema_schedule_category(schema, SCHEDULE_GROUPS)}",
            trigger_rule="all_done",
        )

        list_tables_from_gs_bucket_raw = PythonOperator(
            task_id=f"{schema}.list_tables_with_changes",
            python_callable=list_changes,
            op_kwargs={
                "bucket_name": gs_bucket_raw,
                "schema": schema,
                "path": _get_path(connection, schema),
            },
            pool="sensor_pool",
        )

        if schema not in Config.NO_WAIT_TIME_SCHEMA:
            wait_task >> list_tables_from_gs_bucket_raw
            wait_previous_end >> wait_task
        else:
            wait_previous_end >> list_tables_from_gs_bucket_raw

        if schema in SchemaConfig.get_compaction_schemas(Config.ENV):
            wait_compaction_end = ExternalTaskSensor(
                task_id="wait_compaction_end",
                external_dag_id=f"datalake_compact_{schema}",
                external_task_id="end",
                execution_date_fn=partial(get_last_compaction_date, schema),
                allowed_states=["success", "upstream_failed", "failed", "skipped"],
                mode="reschedule",
                poke_interval=300,
                pool="sensor_pool",
                timeout=900,
                soft_fail=True,
            )
            wait_compaction_end >> wait_previous_end

        if not schema.startswith("ninjamart"):
            get_conn = PythonOperator(
                task_id=f"get_{schema}_conn",
                python_callable=db.get_schema_conn,
                op_args=[schema, DatabaseConfig.MYSQL_CONNECTIONS]
            )

        list_tables_to_process = BranchPythonOperator(
            task_id=f"get_tables_with_changes_{schema}",
            python_callable=list_table_tasks,
            op_args=[schema]
        )

        check_and_extract_delta_task = None

        for table in tables:
            process_cdc_messages_default_var = {}
            process_cdc_messages_default_list = []
            process_cdc_task_id = f"process_cdc_messages_{schema}_{table}"
            process_cdc_task_id_old = f"process_cdc_messages_{schema}_{table}_v1"
            process_cdc = SparkSubmitOperator(
                task_id=process_cdc_task_id_old,
                name=kebab_case(f"cdc-{schema}-{table}"),
                execution_timeout=Timeout.FORTY_FIVE_MINUTES,
                application=f"{Config.TASKS_PATH}/post_process_cdc.py",
                application_args=[
                    Config.ENV,
                    schema,
                    table,
                    "{{ ts }}",
                    str(Config.NUM_INTERVALS),
                    f"""{{{{
                        var.json.pii_fields |
                        extract('{schema}', {process_cdc_messages_default_var}) |
                        extract('{table}', {process_cdc_messages_default_list})
                    }}}}""",
                ],
                conn_id="spark_default",
                conf={
                    **(SparkConfig.get_spark_conf(Config.ENV, JobType.CDC.value)["large"] if process_cdc_task_id in
                                                                                             TableConfig.TABLE_SIZES[
                                                                                                 "large"] else
                       SparkConfig.get_spark_conf(Config.ENV, JobType.CDC.value)["small"]),
                },
            )

            choose_spark_operator_task = BranchPythonOperator(
                task_id=f"choose_spark_operator_{schema}_{table}",
                python_callable=choose_spark_operator,
                provide_context=True,
                op_args=[schema, table]
            )

            config_process = helper.build_config(
                schema=schema, table=table, env=Config.ENV,
                image=f"{Config.REGISTRY}/{Config.IMAGE_NAME}:{Config.TAG}",
                task_type="process",
                group=JobType.CDC.value,
                table_sizes_airflow=table_sizes_airflow
            )
            config_process['table_sizes_airflow'] = table_sizes_airflow
            config_process['spark_config'] = {**SparkConfig.get_spark_conf(Config.ENV, "cdc")[SparkConfig.TABLES_TO_SIZE_CDC.get(process_cdc_task_id, "medium")]}
            application_file_process = spark_app_utils.generate_application_file_for_task("post_process_cdc",
                                                                                          Config.TASKS_PATH, schema,
                                                                                          table, config_process)

            process_cdc_k8s = SparkK8s(
                task_id=process_cdc_task_id,
                namespace=os.environ["AIRFLOW__KUBERNETES__NAMESPACE"],
                application_file=application_file_process,
                kubernetes_conn_id="kubernetes_default",
                startup_timeout_seconds=3600,
                in_cluster=True,
                cluster_context="data-context",
                reattach_on_restart=True,
                get_logs=True,
                log_events_on_failure=True,
                delete_on_termination=True,
                arguments=[
                    Config.ENV,
                    schema,
                    table,
                    "{{ ts }}",
                    str(Config.NUM_INTERVALS),
                    f"""{{{{
                        var.json.pii_fields |
                        extract('{schema}', {process_cdc_messages_default_var}) |
                        extract('{table}', {process_cdc_messages_default_list})
                    }}}}"""
                ],
                env_vars={"EXECUTION_DATE": "{{ execution_date }}"},
                pool="cdc_pool"
            )

            delta_task_id_old = f"{'pii_' if is_pii else ''}delta_{schema}_{table}_v1"
            delta_task_id = f"{'pii_' if is_pii else ''}delta_{schema}_{table}"
            delta_default_airflow_var = ["id"]

            delta_task = SparkSubmitOperator(
                task_id=delta_task_id_old,
                name=kebab_case(delta_task_id_old),
                execution_timeout=Timeout.FORTY_FIVE_MINUTES,
                application=f"{Config.TASKS_PATH}/update_delta.py",
                application_args=[
                    Config.ENV,
                    schema,
                    table,
                    "{{ ts }}",
                    f"{{{{ var.json.with_other_pk | extract('{schema}.{table}', {delta_default_airflow_var}) }}}}",
                    f"{{{{ ti.xcom_pull(dag_id='datalake_cdc_{schema}', task_ids='get_{schema}_conn') }}}}",
                    f"{is_pii}",
                ],
                conn_id="spark_default",
                conf={
                    **(SparkConfig.get_spark_conf(Config.ENV, JobType.CDC.value)[
                        TableConfig.TABLE_SIZES.get(delta_task_id, "medium")]),
                    "spark.databricks.delta.schema.autoMerge.enabled": "true",
                },
            )
            config_delta = helper.build_config(
                schema=schema, table=table, env=Config.ENV,
                image=f"{Config.REGISTRY}/{Config.IMAGE_NAME}:{Config.TAG}",
                task_type="delta",
                table_sizes_airflow=table_sizes_airflow,
                group=JobType.CDC.value
            )
            config_delta['table_sizes_airflow'] = table_sizes_airflow
            config_delta['spark_config'] = {**SparkConfig.get_spark_conf(Config.ENV, "cdc")[SparkConfig.TABLES_TO_SIZE_CDC.get(delta_task_id, "medium")]}
            application_file_delta = spark_app_utils.generate_application_file_for_task("update_delta",
                                                                                        Config.TASKS_PATH, schema,
                                                                                        table, config_delta)
            delta_task_k8s = SparkK8s(
                task_id=delta_task_id,
                namespace=os.environ["AIRFLOW__KUBERNETES__NAMESPACE"],
                application_file=application_file_delta,
                kubernetes_conn_id="kubernetes_default",
                in_cluster=True,
                startup_timeout_seconds=1800,
                cluster_context="data-context",
                reattach_on_restart=True,
                get_logs=True,
                log_events_on_failure=True,
                delete_on_termination=True,
                do_xcom_push=False,
                arguments=[
                    Config.ENV,
                    schema,
                    table,
                    "{{ ts }}",
                    f"{{{{ var.json.with_other_pk | extract('{schema}.{table}', {delta_default_airflow_var}) }}}}",
                    f"{{{{ ti.xcom_pull(dag_id='datalake_cdc_{schema}', task_ids='get_{schema}_conn') }}}}",
                    f"{is_pii}",
                ],
                env_vars={"EXECUTION_DATE": "{{ execution_date }}"},
                pool="cdc_pool",
            )

            choose_spark_operator_task >> process_cdc
            choose_spark_operator_task >> process_cdc_k8s
            process_cdc >> delta_task
            process_cdc_k8s >> delta_task_k8s

            if not schema.startswith("ninjamart"):
                check_and_extract_delta_task = PythonOperator(
                    task_id=f"check_and_extract_delta{'_pii' if is_pii else ''}_{schema}_{table}",
                    python_callable=check_and_extract_delta_metrics,
                    op_args=[Config.ENV, schema, table, delta_task_id, is_pii],
                    on_failure_callback=partial(clear_delta_callback, delta_task),
                    pool="sensor_pool",
                )

                list_tables_to_process >> choose_spark_operator_task
                delta_task >> check_and_extract_delta_task
                delta_task_k8s >> check_and_extract_delta_task
            else:
                list_tables_to_process >> choose_spark_operator_task

            def trigger_raw_to_delta_comparison_task(schema, table, **kwargs):
                return TriggerDagRunOperator(
                    task_id=f"trigger_raw_to_delta_comparison_{schema}_{table}",
                    wait_for_completion=False,
                    trigger_dag_id='raw_to_delta_sanity_dag',
                    conf={"schema": schema, "table": table, "execution_date": "{{ execution_date }}"},
                )

            trigger_task = trigger_raw_to_delta_comparison_task(schema, table)
            if check_and_extract_delta_task is not None:
                check_and_extract_delta_task >> trigger_task

        if schema.startswith("ninjamart"):
            list_tables_from_gs_bucket_raw >> list_tables_to_process
        else:
            list_tables_from_gs_bucket_raw >> [get_conn] >> list_tables_to_process

    return dag


for schema, tables in DatabaseConfig.MYSQL_TABLES.items():
    globals()[f"datalake_cdc_{schema}"] = create_cdc_dag(schema, tables)
